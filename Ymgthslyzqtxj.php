<?php

namespace app\zt2025\controller;

use app\BaseController;
use app\common\controller\OldController;
use app\common\model\Wechat;
use app\common\controller\ZtController;

use think\facade\View;
use think\facade\Cache;
use think\facade\Db;

use wechat\Jsauth;
use wechat\Jssdk;

class Ymgthslyzqtxj extends OldController
{

    public function initialize()
    {
        parent::initialize();
        $this->config_id = 227;
        $this->fid = 517;
        $this->session_id = '2025_ymgthslyzqtxj_' . $this->fid;
        $this->tableName = 'moo_form_data_2025_3';
        $this->getConfig(); //获取微信配置信息 
        //测试授权
        $this->ztdebug('ymgthslyzqtxj');
        //授权，key和测试授权保持一致才能起作用
        $this->Oauth('', 'snsapi_userinfo', 'ymgthslyzqtxj');
        //统计
        totalviews_new($this->fid);

        $this->rotation_key = $this->session_id . '_rotation';

        //活动状态
        if (time() < $this->configWx['reg_start_time']) {
            $this->endtime = 1;
        } elseif (time() > $this->configWx['reg_end_time']) {
            $this->endtime = 2;
        }
    }

    public function index()
    {
        return view();
    }
}
