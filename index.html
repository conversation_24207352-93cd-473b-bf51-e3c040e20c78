<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>月满归途徽商礼韵 中秋探乡记</title>
    <style>
        *{
            margin: 0;
            padding: 0;
            border: none;
            box-sizing: border-box;
        }
        body {
            margin: 0;
            padding: 0;
            background: #ede8d9 url(img/bj.jpg) no-repeat center center/100% auto;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: Arial, sans-serif;
        }
        canvas {
            display: block;
        }
        #gameInfo {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            font-size: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            z-index: 1000;
        }

        #countdown {
            position: fixed !important;
            top: 50% !important;
            left: 50% !important;
            transform: translate(-50%, -50%) !important;
            font-size: 40px !important;
            color: white !important;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8) !important;
            z-index: 1001 !important;
            font-weight: bold !important;
            font-family: Arial, sans-serif !important;
        }

        #gameOver {
            position: fixed !important;
            top: 50% !important;
            left: 50% !important;
            transform: translate(-50%, -50%) !important;
            text-align: center !important;
            color: white !important;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8) !important;
            z-index: 1001 !important;
            font-family: Arial, sans-serif !important;
            background: rgba(0, 0, 0, 0.7) !important;
            padding: 30px !important;
            border-radius: 10px !important;
        }

        #gameOver h1 {
            font-size: 48px !important;
            margin: 0 0 20px 0 !important;
            color: #FF6B6B !important;
        }

        #gameOver p {
            font-size: 24px !important;
            margin: 10px 0 !important;
        }


    </style>
</head>
<body>
    <div id="gameInfo">
        <div>金币: <span id="coins">0</span></div>
        <div>距离: <span id="distance">0</span>m</div>
        <div>FPS: <span id="fps">0</span></div>
    </div>

    <div id="countdown" style="display: none;"></div>

    <div id="gameOver" style="display: none;">
        <h1>游戏结束!</h1>
        <p>最终得分: <span id="finalScore">0</span></p>
        <p>跑了 <span id="finalDistance">0</span>米</p>
        <p>点击/触摸屏幕重新开始</p>
    </div>

    <!-- 引入Three.js -->
    <script src="https://ztimg.hefei.cc/static/common/js/libs/three.min.js"></script>

    <script>
        // ========== 配置参数 ==========
        const CONFIG = {
            // 纹理路径
            textures: {
                background: 'img/bj.jpg',
                cityGate: 'img/door.png',
                street: 'img/left.png', // 保留作为备用
                // road: 'img/road.png',
                road: 'img/wall.jpg',
                player: 'img/player.png'
            },

            // 3D街道模型配置
            streetModel: {
                objPath: 'mould/street1/base_simplify.obj',
                textures: {
                    diffuse: 'mould/street1/texture_diffuse.jpg',
                },
                // 模型生成数量配置（用于性能优化）
                leftStreetCount: 7,     // 左侧街道模型数量（原来是-4到4共9个）
                rightStreetCount: 7,    // 右侧街道模型数量（原来是-4到4共9个）
                roadSegmentCount: 5     // 道路段数量（原来是-2到2共5个）
            },

            // 3D城门模型配置
            cityGateModel: {
                objPath: 'mould/door/base.obj',
                textures: {
                    diffuse: 'mould/door/texture_diffuse.png',
                }
            },

            // 高度配置
            cityGateHeight: 30,      // 城门高度（米）
            streetHeight: 12,        // 街道高度（米）
            playerHeight: 2,        // 人物高度（米）

            // 位置配置
            cityGateDistance: 100,  // 城门距离起点的距离（米）
            roadWidth: 6,          // 赛道宽度（米）

            // 摄像机配置
            cameraDistance: 2,     // 摄像机距离玩家的距离
            cameraHeight: 2.5,       // 摄像机相对玩家的高度
            cameraAngle: 120        // 摄像机视野角度（度）
        };

        // 游戏变量
        let scene, camera, renderer;
        let player, road, gameObjects = [];
        let sceneryObjects = [];
        let gameState = 'loading'; // loading, countdown, playing, gameOver
        let countdownTimer = 3;
        let playerSpeed = 3; // 玩家前进速度（米/秒），与FPS无关
        let score = 0;
        let distance = 0;
        let keys = {};
        const DEBUG_LOG = false;
        let clock = new THREE.Clock();
        let accumulator = 0;
        const FIXED_DT = 1 / 60;
        let backgroundPlaneWidth = 15; // 默认背景宽度

        // 模型加载状态跟踪
        let modelsLoaded = {
            leftStreet: false,
            rightStreet: false,
            cityGate: false,
            road: false,
            player: false
        };

        // 玩家状态
        let playerX = 0; // 玩家X位置（可在道路宽度范围内自由移动）
        let playerZ = 0;

        // 障碍间距控制
        const obstacleSpawnBaseProb = 0.10; // 基础障碍生成概率
        const baseObstacleGap = 60; // 基础最小Z间距（米）
        let lastObstacleZ = -Infinity; // 最近一次障碍的Z
        const baseCoinGap = 12; // 金币最小Z间距（米）
        let lastCoinZ = -Infinity; // 最近一次金币的Z
        const MAX_COINS = 60; // 活跃金币上限
        const MAX_OBSTACLES = 40; // 活跃障碍上限
        let lastSpawnZ = 0; // 距离驱动的生成节流

        // 共享几何与材质，减少GC与内存占用
        const shared = {
            coinGeometry: new THREE.CylinderGeometry(0.5, 0.5, 0.2, 8),
            coinMaterial: new THREE.MeshLambertMaterial({ color: 0xFFD700 }),
            obstacleGeometry: new THREE.BoxGeometry(1.5, 1.5, 1.5),
            obstacleMaterial: new THREE.MeshLambertMaterial({ color: 0x555555 }),
            treeTrunkGeometry: new THREE.CylinderGeometry(0.3, 0.4, 3, 8),
            treeTrunkMaterial: new THREE.MeshLambertMaterial({ color: 0x8B4513 }),
            treeLeavesGeometry: new THREE.SphereGeometry(1.5, 8, 8),
            treeLeavesMaterial: new THREE.MeshLambertMaterial({ color: 0x228B22 }),
            houseBodyGeometry: new THREE.BoxGeometry(3, 3, 3),
            houseBodyMaterial: new THREE.MeshLambertMaterial({ color: 0x8B4513 }),
            houseRoofGeometry: new THREE.ConeGeometry(2.2, 1.5, 4),
            houseRoofMaterial: new THREE.MeshLambertMaterial({ color: 0xDC143C }),
            signPoleGeometry: new THREE.CylinderGeometry(0.1, 0.1, 3, 8),
            signPoleMaterial: new THREE.MeshLambertMaterial({ color: 0x8B4513 }),
            signBoardGeometry: new THREE.BoxGeometry(2, 1.5, 0.2),
            signBoardMaterial: new THREE.MeshLambertMaterial({ color: 0x4169E1 })
        };
        const tempLookAt = new THREE.Vector3();

        // 检查所有模型是否加载完成
        function checkAllModelsLoaded() {
            const allLoaded = Object.values(modelsLoaded).every(loaded => loaded);
            if (allLoaded && gameState === 'loading') {
                console.log('所有模型加载完成，开始倒计时');
                gameState = 'countdown';
                showCountdown();
            }
        }

        // 标记模型加载完成
        function markModelLoaded(modelName) {
            modelsLoaded[modelName] = true;
            console.log(`${modelName} 模型加载完成`);
            checkAllModelsLoaded();
        }

        // 笔直对齐的路边物体参数与追踪
        // 根据配置的赛道宽度计算边缘位置。房子宽3(半宽1.5)，树冠半径约1.5
        const leftHouseX = -CONFIG.roadWidth/2 - 1.5;  // 房子紧贴左边缘
        const rightTreeX = CONFIG.roadWidth/2 + 1.5;   // 树紧贴右边缘
        let lastHouseZ = 200;         // 初始化后将会更新
        let lastTreeZ = 200;          // 初始化后将会更新

        // 初始化Three.js场景
        function initThreeJS() {
            // 创建场景
            scene = new THREE.Scene();
            // scene.fog = new THREE.Fog(0xffffff, 50, 200); // 移除雾效

            // 创建摄像机 (透视摄像机) - 调整视野角度减少透视问题
            camera = new THREE.PerspectiveCamera(CONFIG.cameraAngle, window.innerWidth / window.innerHeight, 0.1, 1000);

            // 创建渲染器
            renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.setClearColor(0x000000, 0); // 透明背景
            // 高分屏清晰渲染
            renderer.setPixelRatio(Math.min(window.devicePixelRatio || 1, 2));
            // 移除阴影系统以提升性能
            renderer.shadowMap.enabled = false;

            // 将渲染器添加到页面
            document.body.appendChild(renderer.domElement);

            // 添加光源
            const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
            scene.add(ambientLight);

            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(10, 20, 5);
            // 移除阴影设置
            scene.add(directionalLight);
        }

        // 创建城门
        function createCityGate() {
            // 尝试加载3D模型
            load3DCityGateModel().then((cityGateModel) => {
                console.log('成功加载3D城门模型');
                console.log('城门模型边界框:', new THREE.Box3().setFromObject(cityGateModel));
                create3DCityGate(cityGateModel);
            }).catch((error) => {
                console.warn('3D城门模型加载失败，使用2D贴图:', error);
                create2DCityGate();
            });
        }

        // 创建3D城门
        function create3DCityGate(cityGateMesh) {
            // 计算模型的边界框来确定缩放
            const box = new THREE.Box3().setFromObject(cityGateMesh);
            const modelSize = box.getSize(new THREE.Vector3());

            // 缩放调整（根据配置的城门高度调整）
            const scale = CONFIG.cityGateHeight / Math.max(modelSize.y, 1);
            cityGateMesh.scale.set(scale, scale, scale);

            // 位置设置：横跨道路，在配置的距离处
            const gateZ = -CONFIG.cityGateDistance; // 负值因为玩家向-Z前进
            cityGateMesh.position.set(0, 0, gateZ); // Y=0贴地
            cityGateMesh.rotation.y = 0; // 面向玩家前进方向

            // 添加到场景
            scene.add(cityGateMesh);

            // 存储城门引用以便后续管理
            window.cityGate = cityGateMesh;

            console.log(`3D城门模型 - 位置: x=0, z=${gateZ}, 缩放: ${scale.toFixed(2)}`);

            // 标记城门加载完成
            markModelLoaded('cityGate');
        }

        // 创建2D城门（备用方案）
        function create2DCityGate() {
            const loader = new THREE.TextureLoader();
            loader.load(CONFIG.textures.cityGate, function(texture) {
                // 调整纹理设置
                texture.wrapS = THREE.ClampToEdgeWrapping;
                texture.wrapT = THREE.ClampToEdgeWrapping;
                texture.magFilter = THREE.LinearFilter;
                texture.minFilter = THREE.LinearMipMapLinearFilter;

                // 基于图片宽高比动态计算城门尺寸
                const img = texture.image;
                const aspect = img && img.height ? (img.width / img.height) : 1; // w/h
                const gateHeight = CONFIG.cityGateHeight; // 使用配置的城门高度
                const gateWidth = gateHeight * aspect; // 根据图片比例自动计算宽度

                const gateGeometry = new THREE.PlaneGeometry(gateWidth, gateHeight);
                const gateMaterial = new THREE.MeshLambertMaterial({
                    map: texture,
                    transparent: true,
                    side: THREE.DoubleSide,
                    opacity: 1
                });

                const gateMesh = new THREE.Mesh(gateGeometry, gateMaterial);

                // 位置设置：横跨道路，在配置的距离处
                const gateZ = -CONFIG.cityGateDistance; // 负值因为玩家向-Z前进
                gateMesh.position.set(0, gateHeight/2, gateZ);
                gateMesh.rotation.y = 0; // 面向玩家前进方向

                // 添加到场景
                scene.add(gateMesh);

                // 存储城门引用以便后续管理
                window.cityGate = gateMesh;

                // 标记城门加载完成
                markModelLoaded('cityGate');

            }, undefined, function(error) {
                console.warn('城门图片加载失败:', error);
                // 即使加载失败也标记为完成，避免阻塞游戏
                markModelLoaded('cityGate');
            });
        }

        // 简化的OBJ加载器
        class SimpleOBJLoader {
            constructor() {
                this.textureLoader = new THREE.TextureLoader();
            }

            async loadOBJ(objPath) {
                try {
                    const response = await fetch(objPath);
                    const objText = await response.text();
                    return this.parseOBJ(objText);
                } catch (error) {
                    throw new Error(`Failed to load OBJ file: ${error.message}`);
                }
            }

            parseOBJ(objText) {
                const vertices = [];
                const normals = [];
                const uvs = [];
                const faces = [];

                const lines = objText.split('\n');

                for (let line of lines) {
                    line = line.trim();
                    if (line.startsWith('v ')) {
                        const parts = line.split(/\s+/);
                        vertices.push(
                            parseFloat(parts[1]),
                            parseFloat(parts[2]),
                            parseFloat(parts[3])
                        );
                    } else if (line.startsWith('vn ')) {
                        const parts = line.split(/\s+/);
                        normals.push(
                            parseFloat(parts[1]),
                            parseFloat(parts[2]),
                            parseFloat(parts[3])
                        );
                    } else if (line.startsWith('vt ')) {
                        const parts = line.split(/\s+/);
                        uvs.push(
                            parseFloat(parts[1]),
                            parseFloat(parts[2])
                        );
                    } else if (line.startsWith('f ')) {
                        const parts = line.split(/\s+/).slice(1);
                        const face = [];
                        for (let part of parts) {
                            const indices = part.split('/');
                            face.push({
                                vertex: parseInt(indices[0]) - 1,
                                uv: indices[1] ? parseInt(indices[1]) - 1 : null,
                                normal: indices[2] ? parseInt(indices[2]) - 1 : null
                            });
                        }
                        faces.push(face);
                    }
                }

                // 创建Three.js几何体
                const geometry = new THREE.BufferGeometry();
                const positions = [];
                const normalsArray = [];
                const uvsArray = [];

                for (let face of faces) {
                    if (face.length >= 3) {
                        // 处理三角形
                        for (let i = 0; i < 3; i++) {
                            const vertexIndex = face[i].vertex;
                            positions.push(
                                vertices[vertexIndex * 3],
                                vertices[vertexIndex * 3 + 1],
                                vertices[vertexIndex * 3 + 2]
                            );

                            if (face[i].normal !== null && normals.length > 0) {
                                const normalIndex = face[i].normal;
                                normalsArray.push(
                                    normals[normalIndex * 3],
                                    normals[normalIndex * 3 + 1],
                                    normals[normalIndex * 3 + 2]
                                );
                            }

                            if (face[i].uv !== null && uvs.length > 0) {
                                const uvIndex = face[i].uv;
                                uvsArray.push(
                                    uvs[uvIndex * 2],
                                    uvs[uvIndex * 2 + 1]
                                );
                            }
                        }

                        // 如果是四边形，添加第二个三角形
                        if (face.length === 4) {
                            const indices = [0, 2, 3];
                            for (let i of indices) {
                                const vertexIndex = face[i].vertex;
                                positions.push(
                                    vertices[vertexIndex * 3],
                                    vertices[vertexIndex * 3 + 1],
                                    vertices[vertexIndex * 3 + 2]
                                );

                                if (face[i].normal !== null && normals.length > 0) {
                                    const normalIndex = face[i].normal;
                                    normalsArray.push(
                                        normals[normalIndex * 3],
                                        normals[normalIndex * 3 + 1],
                                        normals[normalIndex * 3 + 2]
                                    );
                                }

                                if (face[i].uv !== null && uvs.length > 0) {
                                    const uvIndex = face[i].uv;
                                    uvsArray.push(
                                        uvs[uvIndex * 2],
                                        uvs[uvIndex * 2 + 1]
                                    );
                                }
                            }
                        }
                    }
                }

                geometry.setAttribute('position', new THREE.Float32BufferAttribute(positions, 3));

                if (normalsArray.length > 0) {
                    geometry.setAttribute('normal', new THREE.Float32BufferAttribute(normalsArray, 3));
                } else {
                    geometry.computeVertexNormals();
                }

                if (uvsArray.length > 0) {
                    geometry.setAttribute('uv', new THREE.Float32BufferAttribute(uvsArray, 2));
                }

                return geometry;
            }
        }

        // 加载3D街道模型
        function load3DStreetModel() {
            return new Promise(async (resolve, reject) => {
                const objLoader = new SimpleOBJLoader();
                const textureLoader = new THREE.TextureLoader();

                try {
                    // 只加载最重要的纹理 - 基础颜色贴图
                    const loadTexture = (path) => {
                        return new Promise((resolve, reject) => {
                            textureLoader.load(path, resolve, undefined, reject);
                        });
                    };

                    // 只加载diffuse纹理，其他的用默认值
                    const diffuseTexture = await loadTexture(CONFIG.streetModel.textures.diffuse);

                    // 设置纹理属性
                    diffuseTexture.wrapS = THREE.RepeatWrapping;
                    diffuseTexture.wrapT = THREE.RepeatWrapping;
                    diffuseTexture.magFilter = THREE.LinearFilter;
                    diffuseTexture.minFilter = THREE.LinearMipMapLinearFilter;

                    // 加载OBJ模型
                    const geometry = await objLoader.loadOBJ(CONFIG.streetModel.objPath);

                    // 创建简化的材质，只使用基础颜色贴图
                    const material = new THREE.MeshLambertMaterial({
                        map: diffuseTexture
                    });

                    // 创建网格对象
                    const mesh = new THREE.Mesh(geometry, material);

                    resolve(mesh);
                } catch (error) {
                    reject(error);
                }
            });
        }

        // 加载3D城门模型
        function load3DCityGateModel() {
            return new Promise(async (resolve, reject) => {
                const objLoader = new SimpleOBJLoader();
                const textureLoader = new THREE.TextureLoader();

                try {
                    // 加载纹理
                    const loadTexture = (path) => {
                        return new Promise((resolve, reject) => {
                            textureLoader.load(path, resolve, undefined, reject);
                        });
                    };

                    // 加载diffuse纹理
                    const diffuseTexture = await loadTexture(CONFIG.cityGateModel.textures.diffuse);

                    // 设置纹理属性
                    diffuseTexture.wrapS = THREE.RepeatWrapping;
                    diffuseTexture.wrapT = THREE.RepeatWrapping;
                    diffuseTexture.magFilter = THREE.LinearFilter;
                    diffuseTexture.minFilter = THREE.LinearMipMapLinearFilter;

                    // 加载OBJ模型
                    const geometry = await objLoader.loadOBJ(CONFIG.cityGateModel.objPath);

                    // 创建材质
                    const material = new THREE.MeshLambertMaterial({
                        map: diffuseTexture
                    });

                    // 创建网格对象
                    const mesh = new THREE.Mesh(geometry, material);

                    resolve(mesh);
                } catch (error) {
                    reject(error);
                }
            });
        }

        // 创建街道（支持左侧和右侧，优先使用3D模型）
        function createStreet(side) {
            // 尝试加载3D模型
            load3DStreetModel().then((streetModel) => {
                console.log(`成功加载3D街道模型 (${side}侧)`);
                console.log('模型边界框:', new THREE.Box3().setFromObject(streetModel));
                console.log('道路宽度:', CONFIG.roadWidth);
                create3DStreet(side, streetModel);
            }).catch((error) => {
                console.warn(`3D街道模型加载失败 (${side}侧)，使用2D贴图:`, error);
                create2DStreet(side);
            });
        }

        // 创建3D街道
        function create3DStreet(side, streetMesh) {
            const isLeft = side === 'left';
            const streetInstances = [];

            // 计算模型的边界框来确定间距和位置
            const box = new THREE.Box3().setFromObject(streetMesh);
            const modelSize = box.getSize(new THREE.Vector3());
            const spacing = Math.max(modelSize.z, 20); // 使用模型Z轴尺寸作为间距，最小20

            // 缩放调整（根据街道模型的实际大小调整）
            const scale = CONFIG.streetHeight / Math.max(modelSize.y, 1); // 根据配置的街道高度调整缩放

            // 使用配置的数量创建街道模型实例
            const modelCount = isLeft ? CONFIG.streetModel.leftStreetCount : CONFIG.streetModel.rightStreetCount;
            // 初始分布：前方(modelCount-1)个，后方1个（以玩家位置为基准）
            const frontCount = modelCount - 1; // 前方模型数量

            // 创建模型：从后方1个到前方(modelCount-1)个
            // i=1是后方，i=0,-1,-2...是前方
            for (let i = 1; i >= -frontCount + 1; i--) {
                const streetClone = streetMesh.clone();

                // 先应用缩放
                streetClone.scale.set(scale, scale, scale);

                // 重新计算缩放后的边界框
                const scaledBox = new THREE.Box3().setFromObject(streetClone);
                const scaledSize = scaledBox.getSize(new THREE.Vector3());

                // 先设置旋转 - 让模型面对街道
                if (isLeft) {
                    // 左侧模型向右转90度，面向道路
                    streetClone.rotation.y = Math.PI / 2;
                } else {
                    // 右侧模型向左转90度，面向道路
                    streetClone.rotation.y = -Math.PI / 2;
                }

                // 旋转后重新计算边界框
                const rotatedBox = new THREE.Box3().setFromObject(streetClone);
                const rotatedSize = rotatedBox.getSize(new THREE.Vector3());

                // 位置设置 - 让街道模型紧贴道路边缘
                // 道路宽度是6米，所以道路边缘在±3的位置
                let xPos;
                if (isLeft) {
                    // 左侧：放在道路左边缘(-3)的外侧
                    xPos = -3 - rotatedSize.x / 2;
                } else {
                    // 右侧：放在道路右边缘(+3)的外侧
                    xPos = 3 + rotatedSize.x / 2;
                }

                const yPos = 0; // 贴地
                const zPos = i * spacing;

                streetClone.position.set(xPos, yPos, zPos);

                console.log(`${side}侧街道模型 - 位置: x=${xPos.toFixed(2)}, 旋转后尺寸: ${rotatedSize.x.toFixed(2)}x${rotatedSize.z.toFixed(2)}`);

                scene.add(streetClone);
                streetInstances.push(streetClone);
            }

            // 存储实例引用
            if (isLeft) {
                window.leftBackgroundInstances = streetInstances;
                window.leftBackgroundSpacing = spacing;
                // 标记左侧街道加载完成
                markModelLoaded('leftStreet');
            } else {
                window.rightBackgroundInstances = streetInstances;
                window.rightBackgroundSpacing = spacing;
                // 标记右侧街道加载完成
                markModelLoaded('rightStreet');
            }
        }

        // 创建2D街道（备用方案）
        function create2DStreet(side) {
            const loader = new THREE.TextureLoader();
            loader.load(CONFIG.textures.street, function(texture) {
                // 调整纹理设置以优化显示效果
                texture.wrapS = THREE.RepeatWrapping;
                texture.wrapT = THREE.RepeatWrapping;
                texture.magFilter = THREE.LinearFilter;
                texture.minFilter = THREE.LinearMipMapLinearFilter;

                // 基于图片宽高比动态计算平面尺寸
                const img = texture.image;
                const aspect = img && img.height ? (img.width / img.height) : 1; // w/h
                const planeHeight = CONFIG.streetHeight; // 使用配置的街道高度
                const planeWidth = planeHeight * aspect; // 保持不变形

                const backgroundGeometry = new THREE.PlaneGeometry(planeWidth, planeHeight);
                const backgroundMaterial = new THREE.MeshBasicMaterial({
                    map: texture,
                    transparent: true,
                    side: THREE.DoubleSide,
                    opacity: 1
                });

                const backgroundMesh = new THREE.Mesh(backgroundGeometry, backgroundMaterial);
                backgroundMesh.frustumCulled = false;

                // 位置与角度 - 根据side参数设置
                const yPos = planeHeight / 2; // 底部贴地，顶部为配置高度
                const isLeft = side === 'left';
                const xPos = isLeft ? -CONFIG.roadWidth / 2 : CONFIG.roadWidth / 2; // 左侧或右侧
                const rotationY = isLeft ? Math.PI / 2 : -Math.PI / 2; // 面向道路

                backgroundMesh.position.set(xPos, yPos, 0);
                backgroundMesh.rotation.y = rotationY;

                scene.add(backgroundMesh);

                // 沿Z方向的拼接间距=平面宽度
                const spacing = planeWidth;

                // 创建连续的背景墙效果，按间距无缝拼接
                const backgroundInstances = [];
                for (let i = -12; i <= 12; i++) {
                    if (i === 0) {
                        backgroundInstances.push(backgroundMesh);
                        continue;
                    }
                    const bgClone = backgroundMesh.clone();
                    bgClone.position.z = i * spacing;
                    scene.add(bgClone);
                    backgroundInstances.push(bgClone);
                }

                // 存储背景实例与间距，以便后续动态管理
                if (isLeft) {
                    window.leftBackgroundInstances = backgroundInstances;
                    window.leftBackgroundSpacing = spacing;
                    // 标记左侧街道加载完成
                    markModelLoaded('leftStreet');
                } else {
                    window.rightBackgroundInstances = backgroundInstances;
                    window.rightBackgroundSpacing = spacing;
                    // 标记右侧街道加载完成
                    markModelLoaded('rightStreet');
                }

            }, undefined, function(error) {
                console.warn(`${side === 'left' ? '左侧' : '右侧'}街道图片加载失败:`, error);
                // 即使加载失败也标记为完成，避免阻塞游戏
                if (isLeft) {
                    markModelLoaded('leftStreet');
                } else {
                    markModelLoaded('rightStreet');
                }
            });
        }

        // 创建左侧街道
        function createLeftStreet() {
            createStreet('left');
        }

        // 创建右侧街道
        function createRightStreet() {
            createStreet('right');
        }

        // 动态更新街道（循环复用，真正无限滚动）
        function updateStreet(side) {
            const instances = side === 'left' ? window.leftBackgroundInstances : window.rightBackgroundInstances;
            if (!instances || instances.length === 0) return;

            const spacing = (side === 'left' ? window.leftBackgroundSpacing : window.rightBackgroundSpacing) ||
                           (instances[0]?.geometry?.parameters?.width) || 60;

            // 根据配置的模型数量动态计算覆盖范围
            const modelCount = side === 'left' ? CONFIG.streetModel.leftStreetCount : CONFIG.streetModel.rightStreetCount;
            // 前方覆盖数量 = 总数量 - 1（后方保留1个）
            const frontCoverage = modelCount - 1;
            const backCoverage = 1; // 后方保留1个

            // 在城门附近增加额外的覆盖范围
            const nearCityGate = Math.abs(playerZ + CONFIG.cityGateDistance) < spacing * 3;
            const extraCoverage = nearCityGate ? 2 : 0;

            // 找到最前端(最小z)与最后端(最大z)的面板
            function scanMinMax() {
                let minZ = Infinity, maxZ = -Infinity, minIdx = -1, maxIdx = -1;
                for (let i = 0; i < instances.length; i++) {
                    const z = instances[i].position.z;
                    if (z < minZ) { minZ = z; minIdx = i; }
                    if (z > maxZ) { maxZ = z; maxIdx = i; }
                }
                return { minZ, maxZ, minIdx, maxIdx };
            }

            let { minZ, maxZ, minIdx, maxIdx } = scanMinMax();

            // 目标覆盖范围：根据模型数量动态调整
            // 前方（玩家前进方向 -Z）覆盖frontCoverage段；身后保留backCoverage段
            // 增加覆盖范围以避免在城门附近出现间隙
            const targetFrontMin = playerZ - spacing * (frontCoverage + extraCoverage); // 需要最小z <= 该值
            const targetBackMax = playerZ + spacing * (backCoverage + extraCoverage);   // 需要最大z >= 该值

            // 如果前方覆盖不足，把最后端面板搬到最前端，直到满足
            // 增加安全检查，避免无限循环
            let frontIterations = 0;
            while (minZ > targetFrontMin && frontIterations < 20) {
                const panel = instances[maxIdx];
                panel.position.z = minZ - spacing;
                ({ minZ, maxZ, minIdx, maxIdx } = scanMinMax());
                frontIterations++;
            }

            // 如果身后覆盖不足，把最前端面板搬到最后端，直到满足
            // 增加安全检查，避免无限循环
            let backIterations = 0;
            while (maxZ < targetBackMax && backIterations < 20) {
                const panel = instances[minIdx];
                panel.position.z = maxZ + spacing;
                ({ minZ, maxZ, minIdx, maxIdx } = scanMinMax());
                backIterations++;
            }
        }

        // 动态更新左侧街道
        function updateLeftStreet() {
            updateStreet('left');
        }

        // 动态更新右侧街道
        function updateRightStreet() {
            updateStreet('right');
        }

        // 创建道路
        function createRoad() {
            const roadGroup = new THREE.Group();

            // 道路参数
            const roadLength = 500;
            const roadWidth = CONFIG.roadWidth; // 使用配置的道路宽度

            // 使用配置的段数创建道路
            const segmentCount = CONFIG.streetModel.roadSegmentCount;
            const halfCount = Math.floor(segmentCount / 2);

            for (let i = -halfCount; i <= halfCount; i++) {
                const segmentGroup = new THREE.Group();
                segmentGroup.position.z = i * roadLength;

                // 道路主体
                const roadGeometry = new THREE.PlaneGeometry(roadWidth, roadLength);
                const roadTexture = new THREE.TextureLoader().load(CONFIG.textures.road);
                roadTexture.wrapS = THREE.RepeatWrapping;
                roadTexture.wrapT = THREE.RepeatWrapping;
                roadTexture.repeat.set(1, 20); // X方向不重复，Z方向重复20次
                // 使用MeshBasicMaterial避免光照影响，保持原始纹理颜色
                const roadMaterial = new THREE.MeshBasicMaterial({
                    map: roadTexture
                });
                const roadMesh = new THREE.Mesh(roadGeometry, roadMaterial);
                roadMesh.rotation.x = -Math.PI / 2;
                roadMesh.position.y = 0;
                segmentGroup.add(roadMesh);

                // 道路边界线（较细的#c9b097颜色）
                const lineGeometry = new THREE.BoxGeometry(0.1, 0.05, roadLength); // 更细的线条
                const lineMaterial = new THREE.MeshLambertMaterial({ color: 0xc9b097 });

                // 左边界线
                const leftBorder = new THREE.Mesh(lineGeometry, lineMaterial);
                leftBorder.position.set(-roadWidth/2, 0.025, 0);
                segmentGroup.add(leftBorder);

                // 右边界线
                const rightBorder = new THREE.Mesh(lineGeometry, lineMaterial);
                rightBorder.position.set(roadWidth/2, 0.025, 0);
                segmentGroup.add(rightBorder);

                // 左侧地面扩展区域 - 使用wall.jpg纹理
                const leftSideGeometry = new THREE.PlaneGeometry(10, roadLength);
                const leftSideTexture = new THREE.TextureLoader().load(CONFIG.textures.road);
                leftSideTexture.wrapS = THREE.RepeatWrapping;
                leftSideTexture.wrapT = THREE.RepeatWrapping;
                leftSideTexture.repeat.set(3, 20);
                const leftSideMaterial = new THREE.MeshBasicMaterial({
                    map: leftSideTexture
                });
                const leftSideMesh = new THREE.Mesh(leftSideGeometry, leftSideMaterial);
                leftSideMesh.rotation.x = -Math.PI / 2;
                leftSideMesh.position.set(-roadWidth/2 - 5, 0, 0);
                segmentGroup.add(leftSideMesh);

                // 右侧地面扩展区域 - 使用wall.jpg纹理
                const rightSideGeometry = new THREE.PlaneGeometry(10, roadLength);
                const rightSideTexture = new THREE.TextureLoader().load(CONFIG.textures.road);
                rightSideTexture.wrapS = THREE.RepeatWrapping;
                rightSideTexture.wrapT = THREE.RepeatWrapping;
                rightSideTexture.repeat.set(3, 20);
                const rightSideMaterial = new THREE.MeshBasicMaterial({
                    map: rightSideTexture
                });
                const rightSideMesh = new THREE.Mesh(rightSideGeometry, rightSideMaterial);
                rightSideMesh.rotation.x = -Math.PI / 2;
                rightSideMesh.position.set(roadWidth/2 + 5, 0, 0);
                segmentGroup.add(rightSideMesh);

                roadGroup.add(segmentGroup);
            }

            scene.add(roadGroup);
            road = roadGroup;

            // 标记道路加载完成
            markModelLoaded('road');
        }

        // 创建玩家
        function createPlayer() {
            const playerGroup = new THREE.Group();

            // 使用图片纹理创建玩家
            const loader = new THREE.TextureLoader();
                        loader.load(CONFIG.textures.player, function(texture) {
                // 调整纹理设置
                texture.wrapS = THREE.ClampToEdgeWrapping;
                texture.wrapT = THREE.ClampToEdgeWrapping;
                texture.magFilter = THREE.LinearFilter;
                texture.minFilter = THREE.LinearMipMapLinearFilter;

                // 基于图片宽高比动态计算玩家尺寸
                const img = texture.image;
                const aspect = img && img.height ? (img.width / img.height) : 1; // w/h
                const playerHeight = CONFIG.playerHeight; // 使用配置的人物高度
                const playerWidth = playerHeight * aspect; // 保持比例不变形

                const playerGeometry = new THREE.PlaneGeometry(playerWidth, playerHeight);
                const playerMaterial = new THREE.MeshLambertMaterial({
                    map: texture,
                    transparent: true,
                    side: THREE.DoubleSide,
                    opacity: 1
                });

                const playerMesh = new THREE.Mesh(playerGeometry, playerMaterial);
                playerMesh.position.y = playerHeight / 2; // 底部贴地，顶部2米高
                // 移除阴影投射

                // 清除之前的子对象（如果有的话）
                while(playerGroup.children.length > 0) {
                    playerGroup.remove(playerGroup.children[0]);
                }

                playerGroup.add(playerMesh);

                // 存储玩家网格引用以便后续使用
                window.playerMesh = playerMesh;

                // 标记玩家加载完成
                markModelLoaded('player');

            }, undefined, function(error) {
                console.warn('玩家图片加载失败，使用默认模型:', error);
                // 如果图片加载失败，使用原来的3D模型作为备用
                createDefaultPlayer();
                // 即使加载失败也标记为完成
                markModelLoaded('player');
            });

            // 设置初始位置
            playerGroup.position.set(0, 0, playerZ);
            // 前进方向为 -Z，人物需转向面朝 -Z
            playerGroup.rotation.y = Math.PI;

            scene.add(playerGroup);
            player = playerGroup;
        }

        // 备用的默认3D玩家模型
        function createDefaultPlayer() {
            // 身体
            const bodyGeometry = new THREE.BoxGeometry(1, 2, 0.5);
            const bodyMaterial = new THREE.MeshLambertMaterial({ color: 0xFF6B6B });
            const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
            body.position.y = 1;
            // 移除阴影投射
            player.add(body);

            // 头部
            const headGeometry = new THREE.SphereGeometry(0.4, 8, 8);
            const headMaterial = new THREE.MeshLambertMaterial({ color: 0xFFE4B5 });
            const head = new THREE.Mesh(headGeometry, headMaterial);
            head.position.y = 2.4;
            // 移除阴影投射
            player.add(head);

            // 眼睛
            const eyeGeometry = new THREE.SphereGeometry(0.1, 4, 4);
            const eyeMaterial = new THREE.MeshLambertMaterial({ color: 0x000000 });

            const leftEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
            leftEye.position.set(-0.15, 2.5, 0.3);
            player.add(leftEye);

            const rightEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
            rightEye.position.set(0.15, 2.5, 0.3);
            player.add(rightEye);
        }

        // 限制玩家X位置在道路范围内
        function clampPlayerX(x) {
            const roadWidth = CONFIG.roadWidth;
            const maxX = roadWidth / 2 - 0.8; // 增加边距，确保玩家在道路范围内
            const minX = -maxX;
            return Math.max(minX, Math.min(maxX, x));
        }

        // 创建金币
        function createCoin(x, z) {
            const coinGroup = new THREE.Group();

            const coin = new THREE.Mesh(shared.coinGeometry, shared.coinMaterial);
            // 移除阴影设置
            coinGroup.add(coin);

            coinGroup.position.set(x, 1, z);
            coinGroup.userData = { type: 'coin', x: x };

            scene.add(coinGroup);
            gameObjects.push(coinGroup);

            return coinGroup;
        }

        // 创建障碍物
        function createObstacle(x, z) {
            const obstacleGroup = new THREE.Group();

            const obstacle = new THREE.Mesh(shared.obstacleGeometry, shared.obstacleMaterial);
            obstacle.position.y = 0.75;
            // 移除阴影投射
            obstacleGroup.add(obstacle);

            obstacleGroup.position.set(x, 0, z);
            obstacleGroup.userData = { type: 'obstacle', x: x };

            scene.add(obstacleGroup);
            gameObjects.push(obstacleGroup);

            return obstacleGroup;
        }

        // 生成游戏对象
        function spawnObjects() {
            // 在更远的地方生成物体，避免突兀出现
            const spawnZ = playerZ - 200; // 反向前进：在玩家前方更小Z生成

            // 节流：每前进一定距离才尝试生成，避免每帧写入
            if (Math.abs(spawnZ - lastSpawnZ) >= 2) {
                lastSpawnZ = spawnZ;

                // 生成金币（受最小间距与上限控制）
                if (gameObjects.filter(o => o.userData.type === 'coin').length < MAX_COINS) {
                    if (Math.random() < 0.15 && Math.abs(spawnZ - lastCoinZ) >= baseCoinGap) {
                        const roadWidth = CONFIG.roadWidth;
                        const x = (Math.random() - 0.5) * (roadWidth - 1); // 在道路宽度内随机位置，留边距
                        createCoin(x, spawnZ);
                        lastCoinZ = spawnZ;
                    }
                }

                // 生成障碍物（受最小间距与上限控制）
                if (gameObjects.filter(o => o.userData.type === 'obstacle').length < MAX_OBSTACLES) {
                    if (Math.random() < obstacleSpawnBaseProb && Math.abs(spawnZ - lastObstacleZ) >= baseObstacleGap) {
                        const roadWidth = CONFIG.roadWidth;
                        const x = (Math.random() - 0.5) * (roadWidth - 1); // 在道路宽度内随机位置，留边距
                        createObstacle(x, spawnZ);
                        lastObstacleZ = spawnZ;
                    }
                }
            }
        }

        // 输入处理
        document.addEventListener('keydown', (e) => {
            keys[e.key] = true;

            if (gameState === 'playing') {
                if (e.key === 'ArrowLeft' || e.key === 'a' || e.key === 'A') {
                    playerX -= 0.2; // 键盘控制时的移动步长
                    playerX = clampPlayerX(playerX);
                }
                if (e.key === 'ArrowRight' || e.key === 'd' || e.key === 'D') {
                    playerX += 0.2; // 键盘控制时的移动步长
                    playerX = clampPlayerX(playerX);
                }
            }
        });

        document.addEventListener('keyup', (e) => {
            keys[e.key] = false;
        });

        // 触摸拖动控制
        let isDragging = false;
        let lastTouchX = 0;

        document.addEventListener('touchstart', (e) => {
            if (gameState === 'gameOver') {
                restartGame();
                return;
            }
            if (gameState === 'playing') {
                isDragging = true;
                lastTouchX = e.touches[0].clientX;
            }
        });

        document.addEventListener('touchmove', (e) => {
            if (gameState !== 'playing' || !isDragging) return;

            e.preventDefault(); // 防止页面滚动
            const currentTouchX = e.touches[0].clientX;
            const deltaX = currentTouchX - lastTouchX;

            // 将屏幕像素移动转换为游戏世界坐标移动
            const sensitivity = 0.01; // 调整灵敏度
            playerX += deltaX * sensitivity;
            playerX = clampPlayerX(playerX);

            lastTouchX = currentTouchX;
        });

        document.addEventListener('touchend', (e) => {
            isDragging = false;
        });

        // 点击重开（方便PC端测试）
        document.addEventListener('click', () => {
            if (gameState === 'gameOver') {
                restartGame();
            }
        });

        // 碰撞检测
        function checkCollisions() {
            for (let i = gameObjects.length - 1; i >= 0; i--) {
                const obj = gameObjects[i];
                const distance = Math.abs(obj.position.z - player.position.z);

                if (distance < 2) {
                    if (obj.userData.type === 'coin') {
                        const xDistance = Math.abs(obj.position.x - player.position.x);
                        if (xDistance < 1.0) { // 调整碰撞范围
                            // 收集金币
                            scene.remove(obj);
                            gameObjects.splice(i, 1);
                            score += 10;
                            document.getElementById('coins').textContent = score;
                        }
                    } else if (obj.userData.type === 'obstacle') {
                        const xDistance = Math.abs(obj.position.x - player.position.x);
                        if (xDistance < 1.0) { // 调整碰撞范围
                            // 碰到障碍物
                            gameState = 'gameOver';
                            showGameOver();
                            return;
                        }
                    }
                }
            }
        }

        // 更新摄像机位置（固定居中视角）
        function updateCamera() {
            // 使用配置的摄像机参数
            const distance = CONFIG.cameraDistance; // 摄像机距离
            const height = CONFIG.cameraHeight;     // 摄像机高度

            // 摄像机位置：X固定为0（道路中央），不跟随玩家左右移动
            camera.position.set(0, player.position.y + height, player.position.z + distance);

            // 摄像机始终看向道路中央前方，保持固定视角
            tempLookAt.set(0, player.position.y + 1, player.position.z - 8);
            camera.lookAt(tempLookAt);
        }

        // 固定步长的游戏逻辑更新
        function gameStep(dt) {
            if (gameState !== 'playing') return;

            // 玩家前进（按秒计，反向：-Z）
            playerZ -= playerSpeed * dt;
            player.position.z = playerZ;

            // 直接设置玩家X位置（无缝平移）
            player.position.x = playerX;

            // 更新距离
            distance = Math.floor(playerZ / 2);
            document.getElementById('distance').textContent = distance;

            // 每帧检查并生成需要的对象
            spawnObjects();

            // 移除远离的对象（可碰撞对象）
            for (let i = gameObjects.length - 1; i >= 0; i--) {
                const obj = gameObjects[i];
                if (obj.position.z > playerZ + 50) {
                    scene.remove(obj);
                    gameObjects.splice(i, 1);
                    continue;
                }
                // 极远的前方也做限制，避免累计
                if (obj.position.z < playerZ - 400) {
                    scene.remove(obj);
                    gameObjects.splice(i, 1);
                }
            }
            // 移除远离的对象（风景对象）
            for (let i = sceneryObjects.length - 1; i >= 0; i--) {
                const obj = sceneryObjects[i];
                if (obj.position.z > playerZ + 80) {
                    scene.remove(obj);
                    sceneryObjects.splice(i, 1);
                    continue;
                }
                if (obj.position.z < playerZ - 500) {
                    scene.remove(obj);
                    sceneryObjects.splice(i, 1);
                }
            }

            // 更新跑道位置（无限跑道效果）
            if (road) {
                // 让跑道跟随玩家移动（反向同样按Z取整）
                road.position.z = Math.floor(playerZ / 500) * 500;
            }

            // 更新左右侧街道位置（动态生成新的背景实例）
            updateLeftStreet();
            updateRightStreet();

            // 检查碰撞
            checkCollisions();

            // 更新摄像机
            updateCamera();
        }

        // 显示倒计时
        function showCountdown() {
            document.getElementById('countdown').style.display = 'block';
            document.getElementById('countdown').textContent = '3';
            clock.elapsedTime = 0;
            clock.start();
        }

        // 显示游戏结束
        function showGameOver() {
            document.getElementById('finalScore').textContent = score;
            document.getElementById('finalDistance').textContent = distance;
            document.getElementById('gameOver').style.display = 'block';
        }

        // 重新开始游戏
        function restartGame() {
            // 重置游戏状态为倒计时（模型已经加载过了）
            gameState = 'countdown';
            countdownTimer = 3;
            score = 0;
            distance = 0;
            playerX = 0;
            playerZ = 0;
            lastObstacleZ = Infinity;
            lastHouseZ = -200;
            lastTreeZ = -200;
            accumulator = 0;

            // 重置玩家位置
            player.position.set(0, 0, 0);

            // 清除所有游戏对象
            for (let obj of gameObjects) {
                scene.remove(obj);
            }
            gameObjects = [];
            for (let obj of sceneryObjects) {
                scene.remove(obj);
            }
            sceneryObjects = [];

            // 重置UI
            document.getElementById('coins').textContent = '0';
            document.getElementById('distance').textContent = '0';
            document.getElementById('gameOver').style.display = 'none';

            showCountdown();
            clock.elapsedTime = 0;
            clock.start();
        }

        // 渲染循环
        function animate() {
            const delta = clock.getDelta();

            // 在loading状态显示加载信息
            if (gameState === 'loading') {
                const loadedCount = Object.values(modelsLoaded).filter(loaded => loaded).length;
                const totalCount = Object.keys(modelsLoaded).length;
                document.getElementById('countdown').style.display = 'block';
                document.getElementById('countdown').textContent = `加载中${loadedCount}/${totalCount}`;
            }

            // 倒计时用真实时间推进
            if (gameState === 'countdown') {
                countdownTimer -= delta;
                document.getElementById('countdown').textContent = Math.ceil(Math.max(0, countdownTimer));
                // 在倒计时阶段保持与正式开始一致的摄像机视角
                updateCamera();
                if (countdownTimer <= 0) {
                    gameState = 'playing';
                    document.getElementById('countdown').style.display = 'none';
                }
            }

            // 固定步长推进游戏逻辑
            if (gameState === 'playing') {
                accumulator += Math.min(delta, 0.1);
                while (accumulator >= FIXED_DT) {
                    gameStep(FIXED_DT);
                    accumulator -= FIXED_DT;
                }
            }

            // FPS 计算：每0.5秒更新一次显示
            if (!window.__fpsSamples) {
                window.__fpsSamples = { sum: 0, count: 0, time: 0 };
            }
            const s = window.__fpsSamples;
            s.sum += (1 / Math.max(0.000001, delta));
            s.count += 1;
            s.time += delta;
            if (s.time >= 0.5) {
                const avgFps = Math.round(s.sum / s.count);
                const el = document.getElementById('fps');
                if (el) el.textContent = String(avgFps);
                s.sum = 0;
                s.count = 0;
                s.time = 0;
            }

            renderer.render(scene, camera);
            requestAnimationFrame(animate);
        }

        // 窗口大小调整
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.setPixelRatio(Math.min(window.devicePixelRatio || 1, 2));
        });

        // 初始化游戏
        function initGame() {
            initThreeJS();

            // 显示加载界面
            document.getElementById('countdown').style.display = 'block';
            document.getElementById('countdown').textContent = '加载中... 0/5';

            // 开始加载所有模型
            createLeftStreet(); // 创建左侧街道
            createRightStreet(); // 创建右侧街道
            createCityGate(); // 创建城门
            createRoad();
            createPlayer();

            // 设置初始摄像机位置（固定在道路中央）
            camera.position.set(0, CONFIG.cameraHeight, CONFIG.cameraDistance);
            camera.lookAt(0, 1, -6);

            // 开始渲染循环（不立即显示倒计时，等模型加载完成）
            animate();
        }

        // 启动游戏
        initGame();
    </script>
</body>
</html>